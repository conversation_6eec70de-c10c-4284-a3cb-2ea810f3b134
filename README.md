
# TechShop - AI-Powered E-commerce Chatbot

A modern, responsive e-commerce web application featuring an intelligent AI chatbot for product recommendations and seamless shopping experience.

## 🚀 Features

### Frontend
- **React + TypeScript**: Modern, type-safe development
- **Responsive Design**: Optimized for desktop, tablet, and mobile
- **AI Chatbot**: Interactive product recommendations and customer support
- **Authentication**: User login/signup with session management
- **Product Management**: Advanced filtering, search, and categorization
- **Shopping Cart**: Add to cart functionality with real-time updates
- **Modern UI**: Clean design with animations and micro-interactions

### Backend Integration Ready
- **REST API Structure**: Ready for Python Flask/Django backend
- **Database Schema**: Designed for 100+ product inventory
- **Authentication**: JWT token-based authentication system
- **Product Search**: Intelligent filtering and recommendation engine

## 🛠 Technologies Used

- **Frontend**: React, TypeScript, Tailwind CSS, ShadCN/UI
- **State Management**: React Context API
- **Routing**: React Router DOM
- **Animations**: CSS transitions and keyframes
- **Icons**: Lucide React
- **Build Tool**: Vite

## 📦 Project Structure

```
src/
├── components/
│   ├── auth/           # Authentication components
│   ├── chat/           # Chatbot interface
│   ├── layout/         # Header, navigation
│   ├── products/       # Product cards, grids
│   └── ui/             # Reusable UI components
├── contexts/           # React Context providers
├── data/               # Mock data and types
├── hooks/              # Custom React hooks
├── pages/              # Main application pages
└── types/              # TypeScript type definitions
```

## 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd techshop
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   Navigate to `http://localhost:8080`

## 💬 Chatbot Features

The AI shopping assistant can help with:

- **Product Search**: "Show me laptops under $1000"
- **Category Browsing**: "I need gaming headphones"
- **Budget Recommendations**: "What's the best phone for $500?"
- **Brand Queries**: "Show me Apple products"
- **General Help**: Product information and availability

### Sample Chatbot Queries

- "I'm looking for a gaming laptop"
- "Show me budget smartphones"
- "What are your best headphones?"
- "I need a camera under $2000"
- "Show me all Apple products"

## 🛍 Product Features

- **100+ Mock Products**: Realistic inventory across multiple categories
- **Categories**: Laptops, Smartphones, Audio, Gaming, Cameras, Smart Home
- **Advanced Filtering**: Price range, brand, category, rating
- **Product Details**: Images, descriptions, ratings, availability
- **Responsive Grid**: Optimized layouts for all screen sizes

## 🔐 Authentication

- **Demo Login**: Use any email and password (6+ characters)
- **Session Management**: Persistent login state
- **User Profiles**: Avatar and account information
- **Secure Logout**: Clear session data

## 🎨 Design System

- **Color Palette**: Professional blue gradient theme
- **Typography**: Clean, readable font hierarchy
- **Components**: Consistent, reusable design elements
- **Animations**: Smooth transitions and micro-interactions
- **Responsive**: Mobile-first design approach

## 📱 Responsive Design

- **Desktop**: Full-featured experience with sidebar navigation
- **Tablet**: Optimized layouts with touch-friendly interactions
- **Mobile**: Streamlined interface with bottom navigation

## 🔄 Backend Integration Guide

This frontend is designed to integrate with a Python Flask/Django backend:

### API Endpoints Needed

```python
# Authentication
POST /api/auth/login
POST /api/auth/register
POST /api/auth/logout

# Products
GET /api/products
GET /api/products/:id
GET /api/products/search?q=query
GET /api/products/filter?category=&brand=&price_min=&price_max=

# Chat
POST /api/chat/message
GET /api/chat/history

# Cart
POST /api/cart/add
GET /api/cart
DELETE /api/cart/:id
```

### Database Schema

```sql
-- Users table
CREATE TABLE users (
    id VARCHAR PRIMARY KEY,
    email VARCHAR UNIQUE,
    name VARCHAR,
    password_hash VARCHAR,
    created_at TIMESTAMP
);

-- Products table
CREATE TABLE products (
    id VARCHAR PRIMARY KEY,
    name VARCHAR,
    description TEXT,
    price DECIMAL,
    category VARCHAR,
    brand VARCHAR,
    rating DECIMAL,
    image_url VARCHAR,
    availability BOOLEAN,
    stock INTEGER,
    created_at TIMESTAMP
);

-- Chat messages table
CREATE TABLE chat_messages (
    id VARCHAR PRIMARY KEY,
    user_id VARCHAR REFERENCES users(id),
    content TEXT,
    sender VARCHAR, -- 'user' or 'bot'
    timestamp TIMESTAMP
);
```

## 🚀 Deployment

1. **Build for production**
   ```bash
   npm run build
   ```

2. **Deploy** to your preferred hosting platform (Vercel, Netlify, etc.)

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🔮 Future Enhancements

- **Payment Integration**: Stripe/PayPal checkout
- **Product Reviews**: User ratings and comments
- **Wishlist**: Save favorite products
- **Order History**: Track past purchases
- **Advanced AI**: More sophisticated chatbot responses
- **Real-time Chat**: WebSocket integration
- **Push Notifications**: Order updates and promotions

## 📞 Support

For support and questions:
- Create an issue in this repository
- Contact: <EMAIL>

---

Built with ❤️ using React, TypeScript, and modern web technologies.
