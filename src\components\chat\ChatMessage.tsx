
import React from 'react';
import { ChatMessage as ChatMessageType } from '@/types';
import ProductCard from '@/components/products/ProductCard';

interface ChatMessageProps {
  message: ChatMessageType;
  onProductSelect?: (product: any) => void;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message, onProductSelect }) => {
  const isUser = message.sender === 'user';

  const formatTimestamp = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} animate-slide-up`}>
      <div className={`max-w-[80%] ${isUser ? 'order-2' : 'order-1'}`}>
        {!isUser && (
          <div className="flex items-center mb-1">
            <div className="h-6 w-6 gradient-primary rounded-full flex items-center justify-center mr-2">
              <span className="text-xs font-bold text-white">AI</span>
            </div>
            <span className="text-xs text-gray-500">Shopping Assistant</span>
          </div>
        )}
        
        <div className={`rounded-lg p-3 ${
          isUser 
            ? 'chat-user ml-auto' 
            : 'chat-bot'
        }`}>
          <p className="text-sm whitespace-pre-line">{message.content}</p>
          
          {/* Product suggestions */}
          {message.products && message.products.length > 0 && (
            <div className="mt-3 space-y-2">
              {message.type === 'product-suggestion' ? (
                <div className="grid gap-2">
                  {message.products.map((product) => (
                    <div
                      key={product.id}
                      onClick={() => onProductSelect?.(product)}
                      className="bg-white rounded-lg p-3 border border-gray-200 cursor-pointer hover:shadow-md transition-all duration-200 hover:bg-blue-50"
                    >
                      <div className="flex items-center space-x-3">
                        <img
                          src={product.image_url}
                          alt={product.name}
                          className="h-12 w-12 object-cover rounded"
                        />
                        <div className="flex-1">
                          <h4 className="font-medium text-sm text-gray-900">{product.name}</h4>
                          <div className="flex items-center justify-between mt-1">
                            <span className="text-sm font-bold text-blue-600">${product.price}</span>
                            <div className="flex items-center">
                              <span className="text-yellow-400 text-xs">★</span>
                              <span className="text-xs text-gray-600 ml-1">{product.rating}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-2">
                  {message.products.map((product) => (
                    <div key={product.id} onClick={() => onProductSelect?.(product)}>
                      <ProductCard product={product} compact />
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
        
        <div className={`text-xs text-gray-400 mt-1 ${isUser ? 'text-right' : 'text-left'}`}>
          {formatTimestamp(message.timestamp)}
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
