
import React from 'react';
import { Product } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface ProductCardProps {
  product: Product;
  onAddToCart?: (product: Product) => void;
  compact?: boolean;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onAddToCart, compact = false }) => {
  const discountedPrice = product.discount 
    ? product.price * (1 - product.discount / 100)
    : product.price;

  return (
    <Card className={`group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 ${compact ? 'h-auto' : 'h-full'}`}>
      <div className="relative">
        <img
          src={product.image_url}
          alt={product.name}
          className={`w-full object-cover rounded-t-lg ${compact ? 'h-32' : 'h-48'}`}
        />
        {product.discount && (
          <Badge className="absolute top-2 left-2 bg-red-500 text-white">
            -{product.discount}%
          </Badge>
        )}
        {!product.availability && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-t-lg">
            <Badge variant="destructive">Out of Stock</Badge>
          </div>
        )}
      </div>
      
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-2">
          <Badge variant="secondary" className="text-xs">
            {product.brand}
          </Badge>
          <div className="flex items-center">
            <span className="text-yellow-400 text-sm">★</span>
            <span className="text-sm text-gray-600 ml-1">{product.rating}</span>
          </div>
        </div>
        
        <h3 className={`font-semibold text-gray-900 mb-2 ${compact ? 'text-sm' : 'text-lg'} line-clamp-1`}>
          {product.name}
        </h3>
        
        {!compact && (
          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
            {product.description}
          </p>
        )}
        
        <div className="flex items-center justify-between">
          <div className="flex flex-col">
            <div className="flex items-center space-x-2">
              <span className={`font-bold text-gray-900 ${compact ? 'text-sm' : 'text-lg'}`}>
                ${discountedPrice.toFixed(2)}
              </span>
              {product.discount && (
                <span className="text-sm text-gray-500 line-through">
                  ${product.price.toFixed(2)}
                </span>
              )}
            </div>
            {product.stock && product.stock < 10 && (
              <span className="text-xs text-orange-600">
                Only {product.stock} left
              </span>
            )}
          </div>
          
          <Button
            size={compact ? "sm" : "default"}
            onClick={() => onAddToCart?.(product)}
            disabled={!product.availability}
            className="gradient-primary text-white hover:opacity-90 transition-opacity"
          >
            {compact ? 'Add' : 'Add to Cart'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductCard;
